import React, { useState, useEffect } from 'react';
import Card, { <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '../components/ui/Card';
import Button from '../components/ui/Button';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { Badge } from '../components/ui/badge';
import { Textarea } from '../components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../components/ui/dialog';
import { CheckCircle, XCircle, Clock, User, Calendar, Package, RefreshCw } from 'lucide-react';
import { giftRedemptionsAPI } from '../lib/api';
import toast from 'react-hot-toast';

interface GiftRedemption {
  _id: string;
  userId: {
    _id: string;
    fullName: string;
    phoneNumber: string;
    dealerCode: string;
  };
  productId: string;
  productName: string;
  productImage: string;
  pointsRequired: number;
  userYearlyPointsAtRedemption: number;
  status: 'pending' | 'approved' | 'denied' | 'cancelled';
  redemptionDate: string;
  processedDate?: string;
  adminNotes?: string;
  createdAt: string;
}

const GiftRedemptions: React.FC = () => {
  const [redemptions, setRedemptions] = useState<GiftRedemption[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<string | null>(null);
  const [selectedRedemption, setSelectedRedemption] = useState<GiftRedemption | null>(null);
  const [adminNotes, setAdminNotes] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'approve' | 'deny' | null>(null);
  const [stats, setStats] = useState({ pending: 0, total: 0 });

  useEffect(() => {
    fetchRedemptions();
  }, []);

  const fetchRedemptions = async () => {
    try {
      setLoading(true);
      const response = await giftRedemptionsAPI.getPending();

      if (response.success) {
        const redemptionData = response.data || [];
        setRedemptions(redemptionData);
        setStats({
          pending: redemptionData.length,
          total: response.pagination?.total || redemptionData.length
        });
      } else {
        toast.error(response.message || 'Failed to fetch redemptions');
      }
    } catch (error: any) {
      console.error('Error fetching redemptions:', error);
      const message = error.response?.data?.message || 'Network error. Please check your connection.';
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async (redemption: GiftRedemption, action: 'approve' | 'deny') => {
    setSelectedRedemption(redemption);
    setActionType(action);
    setAdminNotes('');
    setDialogOpen(true);
  };

  const processRedemption = async () => {
    if (!selectedRedemption || !actionType) return;

    // Validate that admin notes are provided for denial
    if (actionType === 'deny' && !adminNotes.trim()) {
      toast.error('Admin notes are required when denying a redemption');
      return;
    }

    try {
      setProcessing(selectedRedemption._id);

      const result = await giftRedemptionsAPI.process(
        selectedRedemption._id,
        actionType,
        adminNotes.trim() || undefined
      );

      if (result.success) {
        toast.success(`Redemption ${actionType}d successfully`);

        // Remove the processed redemption from the list
        setRedemptions(prev => prev.filter(r => r._id !== selectedRedemption._id));

        // Update stats
        setStats(prev => ({
          ...prev,
          pending: prev.pending - 1
        }));

        setDialogOpen(false);
        setSelectedRedemption(null);
        setActionType(null);
        setAdminNotes('');
      } else {
        toast.error(result.message || `Failed to ${actionType} redemption`);
      }
    } catch (error: any) {
      console.error(`Error ${actionType}ing redemption:`, error);
      const message = error.response?.data?.message || `Network error. Failed to ${actionType} redemption`;
      toast.error(message);
    } finally {
      setProcessing(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'approved':
        return <Badge variant="outline" className="text-green-600 border-green-600"><CheckCircle className="w-3 h-3 mr-1" />Approved</Badge>;
      case 'denied':
        return <Badge variant="outline" className="text-red-600 border-red-600"><XCircle className="w-3 h-3 mr-1" />Denied</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Gift Redemptions</h1>
            <p className="text-gray-600 mt-1">Manage and process gift redemption requests</p>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <LoadingSpinner size="lg" />
          <p className="text-gray-500">Loading redemption requests...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gift Redemptions</h1>
          <p className="text-gray-600 mt-1">Manage and process gift redemption requests</p>
        </div>
        <Button
          onClick={fetchRedemptions}
          variant="outline"
          disabled={loading}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Requests</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Requests</p>
                <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Processing Rate</p>
                <p className="text-2xl font-bold text-green-600">
                  {stats.total > 0 ? Math.round(((stats.total - stats.pending) / stats.total) * 100) : 0}%
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {redemptions.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No pending redemptions found</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {redemptions.map((redemption) => (
            <Card key={redemption._id} className="overflow-hidden">
              <CardHeader className="pb-4">
                <div className="flex justify-between items-start">
                  <div className="flex items-center space-x-4">
                    <img
                      src={redemption.productImage}
                      alt={redemption.productName}
                      className="w-16 h-16 object-cover rounded-lg"
                    />
                    <div>
                      <CardTitle className="text-lg">{redemption.productName}</CardTitle>
                      <p className="text-sm text-gray-600">
                        {redemption.pointsRequired} points required
                      </p>
                    </div>
                  </div>
                  {getStatusBadge(redemption.status)}
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-gray-500" />
                    <div>
                      <p className="font-medium">{redemption.userId.fullName}</p>
                      <p className="text-sm text-gray-600">
                        {redemption.userId.dealerCode} • {redemption.userId.phoneNumber}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">Requested</p>
                      <p className="text-sm text-gray-600">
                        {formatDate(redemption.redemptionDate)}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm">
                    <span className="font-medium">User's Yearly Points:</span> {redemption.userYearlyPointsAtRedemption}
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">Points Required:</span> {redemption.pointsRequired}
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">Remaining After Redemption:</span> {redemption.userYearlyPointsAtRedemption - redemption.pointsRequired}
                  </p>
                </div>

                {redemption.status === 'pending' && (
                  <div className="flex flex-wrap gap-3 pt-4">
                    <Button
                      onClick={() => handleAction(redemption, 'approve')}
                      disabled={processing === redemption._id}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Approve
                    </Button>
                    <Button
                      onClick={() => handleAction(redemption, 'deny')}
                      disabled={processing === redemption._id}
                      variant="danger"
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      Deny
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Copy user details to clipboard for easy contact
                        const userInfo = `Name: ${redemption.userId.fullName}\nPhone: ${redemption.userId.phoneNumber}\nDealer Code: ${redemption.userId.dealerCode}\nProduct: ${redemption.productName}\nPoints: ${redemption.pointsRequired}`;
                        navigator.clipboard.writeText(userInfo);
                        toast.success('User details copied to clipboard');
                      }}
                    >
                      <User className="w-4 h-4 mr-2" />
                      Copy Details
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {actionType === 'approve' ? 'Approve' : 'Deny'} Redemption
            </DialogTitle>
          </DialogHeader>
          
          {selectedRedemption && (
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Redemption Details</h4>
                <p><span className="font-medium">Product:</span> {selectedRedemption.productName}</p>
                <p><span className="font-medium">User:</span> {selectedRedemption.userId.fullName}</p>
                <p><span className="font-medium">Points:</span> {selectedRedemption.pointsRequired}</p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Admin Notes {actionType === 'deny' && '(Required for denial)'}
                </label>
                <Textarea
                  value={adminNotes}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setAdminNotes(e.target.value)}
                  placeholder={`Add notes for this ${actionType}al...`}
                  rows={3}
                />
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                <p className="text-sm text-yellow-800">
                  <strong>Warning:</strong> This action cannot be undone.
                  {actionType === 'approve' && ` ${selectedRedemption.pointsRequired} points will be deducted from the user's account.`}
                  {actionType === 'deny' && ' The user will be notified of the denial.'}
                </p>
              </div>

              <div className="flex space-x-3">
                <Button
                  onClick={processRedemption}
                  disabled={processing === selectedRedemption._id || (actionType === 'deny' && !adminNotes.trim())}
                  className={actionType === 'approve' ? 'bg-green-600 hover:bg-green-700' : ''}
                  variant={actionType === 'deny' ? 'danger' : 'primary'}
                >
                  {processing === selectedRedemption._id ? 'Processing...' : `Confirm ${actionType === 'approve' ? 'Approval' : 'Denial'}`}
                </Button>
                <Button variant="outline" onClick={() => setDialogOpen(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GiftRedemptions;
