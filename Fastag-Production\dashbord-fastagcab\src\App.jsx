import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'
import LoginPage from './pages/LoginPage'
import DashboardLayout from './components/layout/DashboardLayout'
import Dashboard from './pages/Dashboard'
import Users from './pages/Users'
import UserDetails from './pages/UserDetails'
import QRCodes from './pages/QRCodes'
import Analytics from './pages/Analytics'
import Settings from './pages/Settings'
import ContentManagement from './pages/ContentManagement'
import GiftRedemptions from './pages/GiftRedemptions'
import ContactForms from './pages/ContactForms'
import PointHistory from './pages/PointHistory'
import LoadingSpinner from './components/ui/LoadingSpinner'
import AdminRechargeDashboard from './pages/AdminRechargeDashboard'

function App() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!user) {
    return (
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    )
  }

  return (
    <DashboardLayout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/users" element={<Users />} />
        <Route path="/users/:id" element={<UserDetails />} />
        <Route path="/qr-codes" element={<QRCodes />} />
        <Route path="/content" element={<ContentManagement />} />
        <Route path="/gift-redemptions" element={<GiftRedemptions />} />
        <Route path="/recharge" element={<AdminRechargeDashboard />} />
        <Route path="/contact-forms" element={<ContactForms />} />
        <Route path="/point-history" element={<PointHistory />} />
        <Route path="/analytics" element={<Analytics />} />
        <Route path="/settings" element={<Settings />} />
        <Route path="/login" element={<Navigate to="/" replace />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </DashboardLayout>
  )
}

export default App
