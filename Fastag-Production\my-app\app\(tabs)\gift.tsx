import React, { useEffect, useState } from "react";
import { useAuth } from '../../contexts/AuthContext';
import {
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ScrollView,
  View,
  Dimensions,
  Alert,
  ActivityIndicator,
  FlatList,
  RefreshControl,
} from "react-native";
import AsyncStorage from '@react-native-async-storage/async-storage';
import FontAwesome from "react-native-vector-icons/FontAwesome";
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from "expo-linear-gradient";
import { ThemedText } from "../../components/ThemedText";
import { ThemedView } from "../../components/ThemedView";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
} from "react-native-reanimated";

// Screen Width
const screenWidth = Dimensions.get("window").width;

// Point History Interface
interface PointHistoryItem {
  _id: string;
  transactionType: 'earned' | 'redeemed' | 'adjusted' | 'expired' | 'bonus';
  pointsChange: number;
  pointsBalance: number;
  source: string;
  description: string;
  createdAt: string;
  metadata?: {
    qrCode?: string;
    productName?: string;
    giftName?: string;
    adminNote?: string;
  };
}

// Sample Data (updated to include recharge items)
const itemsData = [
  {
    id: "0",
    name: "Mobile Recharge - 1 Month",
    points: 500,
    image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },
  {
    id: "1",
    name: "T-Shirt",
    points: 500,
    image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },
  {
    id: "2",
    name: "Earbuds",
    points: 1000,
    image: "https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },
  {
    id: "3",
    name: "Pressure Cooker",
    points: 2000,
    image: "https://images.thdstatic.com/productImages/23e81030-e7fa-4497-8e88-9d576d2e2dc6/svn/barton-stovetop-pressure-cookers-99901-h1-64_1000.jpg",
  },
  {
    id: "4",
    name: "4 Burner Gas Oven",
    points: 5000,
    image: "https://happycredit.in/cloudinary_opt/blog/sunflame-pride-4-burner-gas-stove-or-2-years-produc-42ad2.webp",
  },
  {
    id: "5",
    name: "LCD Television + Health Insurance 1 Lakh",
    points: 10000,
    image: "https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },
  {
    id: "6",
    name: "Godrej Almirah / Smartphone",
    points: 20000,
    image: "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },
  {
    id: "7",
    name: "AC 1.5 Tonne",
    points: 50000,
    image: "https://www.livemint.com/lm-img/img/2024/07/01/1600x900/inverter_ac_1719830746772_1719830747064.png",
  },
  {
    id: "8",
    name: "1 Person Thailand Trip + Shopping Voucher Rs.15,000",
    points: 75000,
    image: "https://thambiliisland.com/wp-content/uploads/2021/12/3.png",
  },
  {
    id: "9",
    name: "10 Grams Gold",
    points: 100000,
    image: "https://images.unsplash.com/photo-1610375461246-83df859d849d?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },
  {
    id: "10",
    name: "2 Person Dubai Trip + Health Insurance 2 Lakh",
    points: 125000,
    image: "https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },
  {
    id: "11",
    name: "Splendor Bike / Scooty",
    points: 150000,
    image: "https://media.zigcdn.com/media/model/2025/Jun/right-side-view-2058808397_600x400.jpg",
  },
  {
    id: "12",
    name: "Electric Scooty",
    points: 175000,
    image: "https://www.amomobility.com/assets/images/model/jaunty/colors/electric-Bikes-red-jaunty.png",
  },
  {
    id: "13",
    name: "3 Person Thailand Trip + Shopping Voucher Rs.35,000",
    points: 200000,
    image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },
  {
    id: "14",
    name: "Bullet",
    points: 250000,
    image: "https://royalenfield.com.au/wp-content/uploads/2023/12/Bullet-350-hero.png",
  },
  {
    id: "15",
    name: "Sofa + AC + TV + Bed + Smartphone + Almirah + Dining Table + Kitchen Bartan Set & More + Health Insurance 5 Lakh",
    points: 300000,
    image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },
  {
    id: "16",
    name: "40 Grams Gold",
    points: 350000,
    image: "https://images.unsplash.com/photo-1610375461246-83df859d849d?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },
  {
    id: "17",
    name: "50 Grams Gold",
    points: 400000,
    image: "https://images.unsplash.com/photo-1610375461246-83df859d849d?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },
  {
    id: "18",
    name: "60 Grams Gold",
    points: 450000,
    image: "https://images.unsplash.com/photo-1610375461246-83df859d849d?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },
  {
    id: "19",
    name: "Car + Health Insurance 10 Lakh",
    points: 551000,
    image: "https://images.unsplash.com/photo-**********-bd32c8ce0db2?w=150&h=150&fit=crop&crop=center&auto=format&q=80",
  },


];

export default function TabTwoScreen() {
  const { user, refreshUserData } = useAuth();

  // State for redemption process
  const [isRedeeming, setIsRedeeming] = useState<string | null>(null);
  const [redemptionHistory, setRedemptionHistory] = useState([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Point History State
  const [pointHistory, setPointHistory] = useState<PointHistoryItem[]>([]);
  const [isLoadingPointHistory, setIsLoadingPointHistory] = useState(false);
  const [refreshingPointHistory, setRefreshingPointHistory] = useState(false);
  const [showPointHistory, setShowPointHistory] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');

  const availablePoints = user?.monthlyPoints || 0;
  const yearlyPoints = user?.yearlyPoints || 0;
  const totalPoints = 500;
  const progressPercentage = (availablePoints / totalPoints) * 100;
  const giftPosition = (screenWidth - 80) * (progressPercentage / 100); // Adjust for padding

  // Animation setup
  const progressAnimation = useSharedValue(0);
  const giftScale = useSharedValue(1);

  // Backend URL - Use local server for testing
  const backendUrl = process.env.EXPO_PUBLIC_API_URL || 'https://fastag.bd1.pro/';

  useEffect(() => {
    // Animate progress bar fill
    progressAnimation.value = withTiming(progressPercentage, { duration: 1000 });
    // Pulse animation for gift icon
    giftScale.value = withSpring(1.2, { damping: 2, stiffness: 80 }, () => {
      giftScale.value = withTiming(1, { duration: 500 });
    });
  }, [progressPercentage]);

  useEffect(() => {
    // Fetch redemption history when component mounts
    if (user) {
      fetchRedemptionHistory();
      fetchPointHistory();
    }
  }, [user]);

  useEffect(() => {
    // Fetch point history when filter changes
    if (user) {
      fetchPointHistory();
    }
  }, [selectedFilter]);

  // Function to fetch user's redemption history
  const fetchRedemptionHistory = async () => {
    setIsLoadingHistory(true);
    try {
      const storedToken = await AsyncStorage.getItem('authToken');
      if (!storedToken) return;

      const response = await fetch(`${backendUrl}api/gifts/my-redemptions?limit=5`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${storedToken}`
        }
      });

      const result = await response.json();
      if (result.success) {
        setRedemptionHistory(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch redemption history:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // Function to fetch point history
  const fetchPointHistory = async (refresh = false) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      if (!token) return;

      if (refresh) {
        setRefreshingPointHistory(true);
      } else {
        setIsLoadingPointHistory(true);
      }

      const queryParams = new URLSearchParams({
        page: '1',
        limit: '10',
        sortOrder: 'desc'
      });

      if (selectedFilter !== 'all') {
        queryParams.append('transactionType', selectedFilter);
      }

      const response = await fetch(`${backendUrl}api/points/history?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.success) {
        setPointHistory(data.data.history);
      } else {
        Alert.alert('Error', data.message || 'Failed to fetch point history');
      }
    } catch (error) {
      console.error('Fetch point history error:', error);
      Alert.alert('Error', 'Failed to fetch point history');
    } finally {
      setIsLoadingPointHistory(false);
      setRefreshingPointHistory(false);
    }
  };

  // Helper functions for point history display
  const getTransactionIcon = (type: string, source: string) => {
    switch (type) {
      case 'earned':
        return source === 'qr_scan' ? 'qr-code' : 'add-circle';
      case 'redeemed':
        return 'gift';
      case 'adjusted':
        return 'settings';
      case 'bonus':
        return 'star';
      default:
        return 'help-circle';
    }
  };

  const getTransactionColor = (pointsChange: number) => {
    return pointsChange > 0 ? '#52b948' : '#ff6b6b';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleFilterChange = (filter: string) => {
    setSelectedFilter(filter);
  };

  // Function to handle gift redemption
  const handleRedemption = async (item: any) => {
    try {
      // Determine if this is a recharge-related product
      const isRechargeProduct = item.name?.toLowerCase().includes('recharge') ||
                               item.name?.toLowerCase().includes('mobile') ||
                               item.name?.toLowerCase().includes('phone') ||
                               item.name?.toLowerCase().includes('data') ||
                               item.name?.toLowerCase().includes('talk time') ||
                               item.name?.toLowerCase().includes('plan');

      // Check points based on product type
      const requiredPoints = item.points;
      const userAvailablePoints = isRechargeProduct ? availablePoints : yearlyPoints; // availablePoints is monthly points
      const pointType = isRechargeProduct ? 'monthly' : 'yearly';

      if (userAvailablePoints < requiredPoints) {
        Alert.alert(
          'Insufficient Points',
          `You need ${requiredPoints} ${pointType} points to redeem this item. You currently have ${userAvailablePoints} ${pointType} points.`,
          [{ text: 'OK' }]
        );
        return;
      }

      // Show confirmation dialog
      Alert.alert(
        'Confirm Redemption',
        `Are you sure you want to redeem ${item.name} for ${requiredPoints} ${pointType} points?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Redeem',
            onPress: () => processRedemption(item, isRechargeProduct),
            style: 'default'
          }
        ]
      );
    } catch (error) {
      console.error('Redemption validation error:', error);
      Alert.alert('Error', 'Failed to validate redemption request');
    }
  };

  // Function to process the redemption
  const processRedemption = async (item: any, isRechargeProduct: boolean = false) => {
    setIsRedeeming(item.id);

    try {
      // Get the stored token
      const storedToken = await AsyncStorage.getItem('authToken');

      if (!storedToken) {
        Alert.alert('Error', 'Authentication required. Please login again.');
        setIsRedeeming(null);
        return;
      }

      console.log('Sending redemption request:', {
        productId: item.id,
        productName: item.name,
        pointsRequired: item.points,
        isRechargeProduct,
        backendUrl
      });

      const requestBody = {
        productId: item.id,
        productName: item.name,
        productImage: item.image,
        pointsRequired: item.points,
        productCategory: item.category || (isRechargeProduct ? 'recharge' : 'general')
      };

      console.log('Request body:', requestBody);

      const response = await fetch(`${backendUrl}api/gifts/redeem`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${storedToken}`
        },
        body: JSON.stringify(requestBody)
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      console.log('Response result:', result);

      if (result.success) {
        // Show success message
        Alert.alert(
          'Success!',
          `Redemption request for ${item.name} submitted successfully! It will be reviewed by admin.`,
          [{ text: 'OK' }]
        );

        // Refresh user data to get updated points
        if (refreshUserData) {
          await refreshUserData();
        }

        // Refresh redemption history
        await fetchRedemptionHistory();
      } else {
        console.error('Redemption failed:', result);
        Alert.alert('Redemption Failed', result.message || 'Failed to process redemption request');
      }
    } catch (error) {
      console.error('Redemption error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Please check your connection and try again.';
      Alert.alert(
        'Error',
        `Network error: ${errorMessage}`
      );
    } finally {
      setIsRedeeming(null);
    }
  };

  const animatedProgressStyle = useAnimatedStyle(() => ({
    width: `${progressAnimation.value}%`,
  }));

  const animatedGiftStyle = useAnimatedStyle(() => ({
    left: giftPosition,
    transform: [{ scale: giftScale.value }],
  }));

  return (
    <ThemedView style={styles.container}>
      {/* Title (unchanged) */}
      <ThemedView style={styles.titleContainer}>
        <ThemedText
          style={{ color: "#f26621", fontStyle: "italic", padding: 10 }}
          type="title"
        >
          Gift & Reward
        </ThemedText>
      </ThemedView>

      {/* Success Message */}
      {showSuccessMessage && (
        <View style={styles.successMessageContainer}>
          <Text style={styles.successMessageText}>{successMessage}</Text>
        </View>
      )}

      {/* Fancy Progress Path */}
      
      {/* Enhanced Progress Path */}
       

      <View style={styles.progressPathContainer}>
        {/* Background Path with Glow */}
        <LinearGradient
          colors={["#e0f7fa", "#b2ebf2"]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.pathBackground}
        />

        {/* Animated Filled Path */}
        <Animated.View style={[styles.pathFill, animatedProgressStyle]}>
          <LinearGradient
            colors={["#ff6d00", "#ff8f00", "#ffab40"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={StyleSheet.absoluteFill}
          />
        </Animated.View>

        {/* Moving Gift Icon with Percentage Tooltip */}
        <Animated.View style={[styles.movingGiftIcon, animatedGiftStyle]}>
          {/* <View style={styles.tooltip}>
            <Text style={styles.tooltipText}>{`${Math.round(progressPercentage)}%`}</Text>
          </View> */}
          <FontAwesome name="gift" size={28} color="#fff" />
        </Animated.View>

      </View>
      <Image source={{ uri: "https://m.media-amazon.com/images/I/41NwNWcsm0L._AC_SY200_QL15_.jpg" }} style={styles.logoImage} />
      {/* Percentage Text */}
      <Text style={styles.creativeProgressText}>
        {availablePoints} / {totalPoints} Points
      </Text>

      {/* Points Display */}
      <ThemedView style={styles.pointsContainer}>
        <ThemedText style={styles.pointText} type="defaultSemiBold">
          Monthly Points: {availablePoints}
        </ThemedText>
        <ThemedText style={[styles.pointText, { fontSize: 14, opacity: 0.8 }]}>
          (For recharge products)
        </ThemedText>
        <ThemedText style={[styles.pointText, { marginTop: 8 }]} type="defaultSemiBold">
          Yearly Points: {yearlyPoints}
        </ThemedText>
        <ThemedText style={[styles.pointText, { fontSize: 14, opacity: 0.8 }]}>
          (For other gifts)
        </ThemedText>
      </ThemedView>

      {/* Point History Section */}
      <ThemedView style={styles.historySection}>
        <View style={styles.historySectionHeader}>
          <ThemedText style={styles.historySectionTitle} type="defaultSemiBold">
            Point History
          </ThemedText>
          <TouchableOpacity
            style={styles.toggleHistoryButton}
            onPress={() => setShowPointHistory(!showPointHistory)}
          >
            <Ionicons
              name={showPointHistory ? "chevron-up" : "chevron-down"}
              size={20}
              color="#f26621"
            />
            <Text style={styles.toggleHistoryText}>
              {showPointHistory ? 'Hide' : 'Show'}
            </Text>
          </TouchableOpacity>
        </View>

        {showPointHistory && (
          <View style={styles.historyContent}>
            {/* Filter Buttons */}
            <View style={styles.filterContainer}>
              {['all', 'earned', 'redeemed', 'adjusted'].map((filter) => (
                <TouchableOpacity
                  key={filter}
                  style={[
                    styles.filterButton,
                    selectedFilter === filter && styles.filterButtonActive
                  ]}
                  onPress={() => handleFilterChange(filter)}
                >
                  <Text style={[
                    styles.filterButtonText,
                    selectedFilter === filter && styles.filterButtonTextActive
                  ]}>
                    {filter.charAt(0).toUpperCase() + filter.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Point History List */}
            {isLoadingPointHistory ? (
              <View style={styles.pointHistoryLoadingContainer}>
                <ActivityIndicator size="small" color="#f26621" />
                <Text style={styles.loadingText}>Loading history...</Text>
              </View>
            ) : pointHistory.length > 0 ? (
              <FlatList
                data={pointHistory}
                keyExtractor={(item) => item._id}
                renderItem={({ item }) => (
                  <View style={styles.pointHistoryItem}>
                    <View style={styles.pointHistoryItemLeft}>
                      <View style={[
                        styles.pointHistoryIconContainer,
                        { backgroundColor: getTransactionColor(item.pointsChange) + '15' }
                      ]}>
                        <Ionicons
                          name={getTransactionIcon(item.transactionType, item.source) as any}
                          size={16}
                          color={getTransactionColor(item.pointsChange)}
                        />
                      </View>
                      <View style={styles.pointHistoryItemContent}>
                        <Text style={styles.pointHistoryItemTitle}>
                          {item.description}
                        </Text>
                        <Text style={styles.pointHistoryItemDate}>
                          {formatDate(item.createdAt)}
                        </Text>
                        {item.metadata?.productName && (
                          <Text style={styles.pointHistoryItemMeta}>
                            {item.metadata.productName}
                          </Text>
                        )}
                      </View>
                    </View>
                    <View style={styles.pointHistoryItemRight}>
                      <Text style={[
                        styles.pointHistoryPointsChange,
                        { color: getTransactionColor(item.pointsChange) }
                      ]}>
                        {item.pointsChange > 0 ? '+' : ''}{item.pointsChange}
                      </Text>
                      <Text style={styles.pointHistoryPointsBalance}>
                        Balance: {item.pointsBalance}
                      </Text>
                    </View>
                  </View>
                )}
                refreshControl={
                  <RefreshControl
                    refreshing={refreshingPointHistory}
                    onRefresh={() => fetchPointHistory(true)}
                    colors={['#f26621']}
                    tintColor="#f26621"
                  />
                }
                style={styles.historyList}
                showsVerticalScrollIndicator={false}
              />
            ) : (
              <View style={styles.emptyHistoryContainer}>
                <Ionicons name="document-text-outline" size={40} color="#ccc" />
                <Text style={styles.emptyHistoryText}>No point history found</Text>
              </View>
            )}
          </View>
        )}
      </ThemedView>

      {/* Recent Redemption History
      {redemptionHistory.length > 0 && (
        <ThemedView style={styles.historyContainer}>
          <ThemedText style={styles.historyTitle} type="defaultSemiBold">
            Recent Redemptions
          </ThemedText>
          {isLoadingHistory ? (
            <ActivityIndicator size="small" color="#f26621" />
          ) : (
            redemptionHistory.slice(0, 3).map((redemption: any, index) => (
              <View key={index} style={styles.historyItem}>
                <View style={styles.historyItemContent}>
                  <ThemedText style={styles.historyItemName}>
                    {redemption.productName}
                  </ThemedText>
                  <ThemedText style={styles.historyItemPoints}>
                    {redemption.pointsRequired} points
                  </ThemedText>
                </View>
                <View style={[
                  styles.statusBadge,
                  redemption.status === 'approved' && styles.statusApproved,
                  redemption.status === 'denied' && styles.statusDenied,
                  redemption.status === 'pending' && styles.statusPending
                ]}>
                  <Text style={styles.statusText}>
                    {redemption.status.charAt(0).toUpperCase() + redemption.status.slice(1)}
                  </Text>
                </View>
              </View>
            ))
          )}
        </ThemedView>
      )} */}

      {/* Items */}
<ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
      {itemsData.map((item) => {
        // Determine if this is a recharge-related product
        const isRechargeProduct = item.name?.toLowerCase().includes('recharge') ||
                                 item.name?.toLowerCase().includes('mobile') ||
                                 item.name?.toLowerCase().includes('phone') ||
                                 item.name?.toLowerCase().includes('data') ||
                                 item.name?.toLowerCase().includes('talk time') ||
                                 item.name?.toLowerCase().includes('plan');

        const requiredPointsAvailable = isRechargeProduct ? availablePoints : yearlyPoints;
        const pointType = isRechargeProduct ? 'Monthly' : 'Yearly';
        const hasInsufficientPoints = requiredPointsAvailable < item.points;

        return (
          <View key={item.id} style={[
            styles.itemContainer,
            hasInsufficientPoints && styles.itemContainerDisabled
          ]}>
            <View style={styles.itemImageContainer}>
              <Image source={{ uri: item.image }} style={[
                styles.itemImage,
                hasInsufficientPoints && styles.itemImageDisabled
              ]} />
              {!hasInsufficientPoints && (
                <View style={styles.eligibleBadge}>
                  <Text style={styles.eligibleBadgeText}>✓</Text>
                </View>
              )}
            </View>
            <View style={styles.itemDetailContainer}>
              <ThemedText type="defaultSemiBold" style={hasInsufficientPoints && styles.textDisabled}>
                {item.name}
              </ThemedText>
              <ThemedText style={hasInsufficientPoints && styles.textDisabled}>
                {item.points} {pointType} Points
              </ThemedText>
              {hasInsufficientPoints && (
                <ThemedText style={styles.insufficientText}>
                  Need {item.points - requiredPointsAvailable} more {pointType.toLowerCase()} points
                </ThemedText>
              )}
              <ThemedView style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[
                    styles.button,
                    (hasInsufficientPoints || isRedeeming === item.id) && styles.buttonDisabled
                  ]}
                  onPress={() => handleRedemption(item)}
                  disabled={hasInsufficientPoints || isRedeeming === item.id}
                >
                  {isRedeeming === item.id ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator size="small" color="#fff" />
                      <Text style={[styles.radeemNowText, { marginLeft: 8 }]}>Processing...</Text>
                    </View>
                  ) : (
                    <Text style={styles.radeemNowText}>
                      {hasInsufficientPoints ? 'Insufficient Points' : 'Redeem Now'}
                    </Text>
                  )}
                </TouchableOpacity>
              </ThemedView>
            </View>
          </View>
        );
      })}
      </ScrollView>
     
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    
  },
  titleContainer: {
    flexDirection: "row",
    gap: 8,
    backgroundColor: "#f2f2f2",
    padding: 10,
    marginVertical: 25,
  },
  pointText: {
    color: "white",
    fontSize: 20,
  },
  pointsContainer: {
    marginVertical: 15,
    alignItems: "center",
    backgroundColor: "#52b948",
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  itemContainer: {
    flexDirection: "row",
    marginVertical: 10,
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  itemImageContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: 10,
  },
  itemDetailContainer: {
    flex: 2,
    paddingLeft: 20,
    justifyContent: "center",
  },
  buttonContainer: {
    marginTop: 10,
  },
  button: {
    padding: 10,
    backgroundColor: "#f26621",
    borderRadius: 10,
    alignItems: "center",
  },
  radeemNowText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
  buttonDisabled: {
    backgroundColor: "#ccc",
    opacity: 0.6,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  itemContainerDisabled: {
    opacity: 0.6,
    backgroundColor: "#f5f5f5",
  },
  itemImageDisabled: {
    opacity: 0.5,
  },
  eligibleBadge: {
    position: "absolute",
    top: -5,
    right: -5,
    backgroundColor: "#52b948",
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  eligibleBadgeText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
  },
  textDisabled: {
    opacity: 0.5,
  },
  insufficientText: {
    color: "#ff6b6b",
    fontSize: 12,
    fontStyle: "italic",
    marginTop: 2,
  },
  historyContainer: {
    marginVertical: 15,
    backgroundColor: "#f8f9fa",
    padding: 15,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#e9ecef",
  },
  historyTitle: {
    color: "#333",
    fontSize: 16,
    marginBottom: 10,
  },
  historyItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  historyItemContent: {
    flex: 1,
  },
  historyItemName: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
  },
  historyItemPoints: {
    fontSize: 12,
    color: "#666",
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 70,
    alignItems: "center",
  },
  statusApproved: {
    backgroundColor: "#d4edda",
  },
  statusDenied: {
    backgroundColor: "#f8d7da",
  },
  statusPending: {
    backgroundColor: "#fff3cd",
  },
  statusText: {
    fontSize: 11,
    fontWeight: "600",
    color: "#333",
  },
  successMessageContainer: {
    backgroundColor: "#d4edda",
    borderColor: "#c3e6cb",
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginVertical: 10,
    alignItems: "center",
  },
  successMessageText: {
    color: "#155724",
    fontSize: 14,
    fontWeight: "600",
    textAlign: "center",
  },

  // Enhanced Progress Bar Styles
  progressPathContainer: {
    height: 40, // Increased height for better visibility
    width: "100%",
    backgroundColor: "#e0f7fa",
    borderRadius: 20,
    overflow: "hidden",
    marginBottom: 15,
    position: "relative",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8, // For Android shadow
  },
  pathBackground: {
    ...StyleSheet.absoluteFillObject,
    borderRadius: 20,
  },
  pathFill: {
    height: "100%",
    borderRadius: 20,
    overflow: "hidden",
    shadowColor: "#ff6d00",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 10,
    elevation: 5,
  },
  movingGiftIcon: {
    position: "absolute",
    top: -3, // Adjusted for larger icon
    backgroundColor: "#ff6d00",
    borderRadius: 24,
    padding: 8,
    elevation: 10,
    zIndex: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  tooltip: {
    position: "absolute",
    top: -30,
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    borderRadius: 8,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  tooltipText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
  },
  creativeProgressText: {
    textAlign: "center",
    fontWeight: "bold",
    color: "#333",
    fontSize: 18,
    marginBottom: 10,
    letterSpacing: 0.5,
  },
  scrollView: {
    flex: 1,
  },
  logoImage: {
    width: 50,
    height: 50,
    marginBottom: 20,
    marginTop: -54,
    borderRadius:50,
    display: "flex",
    alignSelf:"flex-end"

  },

  // Point History Styles
  historySection: {
    marginVertical: 15,
    backgroundColor: "#f8f9fa",
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#e9ecef",
    overflow: 'hidden',
  },
  historySectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  historySectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  toggleHistoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
    backgroundColor: '#f26621' + '10',
  },
  toggleHistoryText: {
    color: '#f26621',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 5,
  },
  historyContent: {
    padding: 15,
  },
  filterContainer: {
    flexDirection: 'row',
    marginBottom: 15,
    flexWrap: 'wrap',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    backgroundColor: '#e9ecef',
    marginRight: 8,
    marginBottom: 8,
  },
  filterButtonActive: {
    backgroundColor: '#f26621',
  },
  filterButtonText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: '#fff',
  },
  historyList: {
    maxHeight: 300,
  },
  pointHistoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  pointHistoryItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  pointHistoryIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  pointHistoryItemContent: {
    flex: 1,
  },
  pointHistoryItemTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  pointHistoryItemDate: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  pointHistoryItemMeta: {
    fontSize: 11,
    color: '#999',
    fontStyle: 'italic',
  },
  pointHistoryItemRight: {
    alignItems: 'flex-end',
  },
  pointHistoryPointsChange: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  pointHistoryPointsBalance: {
    fontSize: 11,
    color: '#666',
  },
  pointHistoryLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginLeft: 10,
    color: '#666',
    fontSize: 14,
  },
  emptyHistoryContainer: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  emptyHistoryText: {
    color: '#999',
    fontSize: 14,
    marginTop: 10,
  },
});