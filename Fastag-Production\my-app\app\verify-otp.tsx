import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@/contexts/AuthContext';
import { router, useLocalSearchParams } from 'expo-router';

export default function VerifyOtpScreen() {
  const { context, phoneNumber, form } = useLocalSearchParams();

  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  const { verifyOtpAndRegister, resendOtpRequest, isLoading } = useAuth();
  const otpRefs = useRef<(TextInput | null)[]>([]);

  useEffect(() => {
    startCountdown();
  }, []);

  const startCountdown = () => {
    setCanResend(false);
    setCountdown(60);
    const interval = setInterval(() => {
      setCountdown(prev => {
        if (prev === 1) {
          clearInterval(interval);
          setCanResend(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleOtpChange = (index: number, value: string) => {
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 5) {
      otpRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (index: number, key: string) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      otpRefs.current[index - 1]?.focus();
    }
  };

  const onResendOTP = async () => {
    try {
      setIsResending(true);
      const result = await resendOtpRequest(phoneNumber as string);
      
      if (result.success) {
        setOtp(['', '', '', '', '', '']);
        startCountdown();
        Alert.alert("Success", "OTP resent successfully!");
      } else {
        Alert.alert("Error", result.message || "Failed to resend OTP.");
      }
    } catch (error) {
      console.error('Resend OTP error:', error);
      Alert.alert("Error", "Network error. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  const onVerify = async () => {
    const otpString = otp.join('');
    
    if (otpString.length !== 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit OTP');
      return;
    }

    try {
      setIsVerifying(true);
      
      if (context === 'register') {
        // For registration, verify OTP and register user
        const formData = JSON.parse(form as string);
        const result = await verifyOtpAndRegister(phoneNumber as string, otpString, formData);
        
        if (result.success) {
          Alert.alert('Success', 'Registration completed successfully!', [
            { text: 'OK', onPress: () => router.replace('/(tabs)') },
          ]);
        } else {
          Alert.alert('Error', result.message || 'Registration failed');
          setOtp(['', '', '', '', '', '']);
        }
      } else if (context === 'forgot') {
        // For forgot password, redirect to reset password screen
        router.push({
          pathname: '/new-password',
          params: { 
            phoneNumber: phoneNumber as string,
            otp: otpString
          }
        });
      }
    } catch (error) {
      console.error('Verification error:', error);
      Alert.alert('Error', 'Verification failed. Please try again.');
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Verify Your Phone Number</Text>
        <Text style={styles.subtitle}>
          We've sent a 6-digit OTP to {phoneNumber || 'your number'}
        </Text>

        <View style={styles.otpContainer}>
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              ref={(ref) => { otpRefs.current[index] = ref; }}
              style={styles.otpInput}
              keyboardType="numeric"
              maxLength={1}
              value={digit}
              onChangeText={value => handleOtpChange(index, value)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(index, nativeEvent.key)}
              textAlign="center"
              autoFocus={index === 0}
              selectTextOnFocus
            />
          ))}
        </View>

        <TouchableOpacity
          style={[styles.verifyButton, (otp.join('').length !== 6 || isVerifying) && styles.disabledButton]}
          onPress={onVerify}
          disabled={isVerifying || otp.join('').length !== 6}
        >
          {isVerifying ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.verifyButtonText}>Verify</Text>
          )}
        </TouchableOpacity>

        <View style={styles.resendContainer}>
          {canResend ? (
            <TouchableOpacity
              style={styles.resendButton}
              onPress={onResendOTP}
              disabled={isResending}
            >
              {isResending ? (
                <ActivityIndicator size="small" color="#0066cc" />
              ) : (
                <Text style={styles.resendText}>Resend OTP</Text>
              )}
            </TouchableOpacity>
          ) : (
            <Text style={styles.countdownText}>Resend OTP in {countdown}s</Text>
          )}
        </View>

        <TouchableOpacity
          style={styles.backButton}
          onPress={() => {
            Alert.alert('Confirm', 'Are you sure you want to go back?', [
              { text: 'Cancel', style: 'cancel' },
              { text: 'OK', onPress: () => router.back() },
            ]);
          }}
        >
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f7f9fb' },
  content: { flex: 1, justifyContent: 'center', padding: 20 },
  title: { fontSize: 28, fontWeight: '700', marginBottom: 10, textAlign: 'center', color: '#333' },
  subtitle: { fontSize: 16, color: '#666', textAlign: 'center', marginBottom: 40, lineHeight: 22 },
  otpContainer: { flexDirection: 'row', justifyContent: 'center', marginBottom: 30 },
  otpInput: {
    borderWidth: 2,
    borderColor: '#0066cc',
    padding: 10,
    borderRadius: 8,
    fontSize: 24,
    textAlign: 'center',
    backgroundColor: '#fff',
    width: 40,
    marginHorizontal: 5,
    fontWeight: '600'
  },
  verifyButton: {
    backgroundColor: '#0066cc',
    padding: 18,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4
  },
  disabledButton: { backgroundColor: '#ccc' },
  verifyButtonText: { color: '#fff', fontSize: 18, fontWeight: '600' },
  resendContainer: { alignItems: 'center', marginBottom: 30 },
  resendButton: { padding: 10 },
  resendText: { color: '#0066cc', fontSize: 16, fontWeight: '600' },
  countdownText: { color: '#666', fontSize: 16 },
  backButton: { alignItems: 'center', padding: 15 },
  backButtonText: { color: '#0066cc', fontSize: 16, fontWeight: '500' }
});
