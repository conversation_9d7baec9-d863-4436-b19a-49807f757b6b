import React, { useState, useRef, useEffect } from "react";
import {
  Plus,
  Download,
  Search,
  Upload,
  Filter,
  X,
  Image,
  Archive,
  RefreshCw,
  AlertCircle,
  QrCode as QrCodeIcon,
  Package,
  Award,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Trash2,
  MoreHorizontal,
  FileText,
  BarChart3
} from "lucide-react";
import QRCode from "react-qr-code";
import * as XLSX from "xlsx";
import J<PERSON><PERSON><PERSON> from "jszip";
import { qrAPI } from "../lib/api";
import toast from "react-hot-toast";
import Card, { CardHeader, CardTitle, CardContent } from '../components/ui/Card';
import Button from '../components/ui/Button';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { Badge } from '../components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../components/ui/dialog';

interface QRCodeData {
  _id?: string;
  id?: number;
  sNo?: number;
  productName: string;
  points: number;
  createdAt?: string;
  createdDate?: string;
  value: number;
  qrCode: string;
  status: string;
  productSize: string;
  createdBy?: string;
  redeemedBy?: string;
  redeemedAt?: string;
}

interface ProductOption {
  size: string;
  points: number;
}
const QRCodes: React.FC = () => {
  const productOptions: ProductOption[] = [
    { size: "0.75 mm", points: 5 },
    { size: "1.0 mm", points: 10 },
    { size: "1.5 mm", points: 15 },
    { size: "2.5 mm", points: 25 },
    { size: "4.0 mm", points: 40 },
    { size: "6.0 mm", points: 60 },
    { size: "10.0 mm", points: 100 },
    { size: "DDH 0.75 mm", points: 5 },
    { size: "DDH 1.0 mm", points: 5 },
    { size: "DDH 1.5 mm", points: 10 },
    { size: "DDH 2.5 mm", points: 15 },
    { size: "DDH 4.0 mm", points: 20 },
  ];

  const [qrCodes, setQrCodes] = useState<QRCodeData[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [exportProgress, setExportProgress] = useState({
    current: 0,
    total: 0,
  });
  const [stats, setStats] = useState({
    totalCodes: 0,
    notRedeemed: 0,
    redeemed: 0,
    totalPoints: 0,
  });
  const [filters, setFilters] = useState({
    productSize: "all",
    status: "all",
    dateRange: "all",
    pointsRange: "all",
  });
  const [formData, setFormData] = useState({
    productSize: "0.75 mm",
    quantity: "1",
    downloadAfterGeneration: false,
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Helper functions for UI
  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'redeemed': return 'default' // Green
      case 'not redeemed': return 'secondary' // Grey
      case 'expired': return 'destructive' // Orange
      default: return 'outline'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'redeemed': return <CheckCircle className="h-3 w-3" />
      case 'not redeemed': return <Clock className="h-3 w-3" />
      case 'expired': return <XCircle className="h-3 w-3" />
      default: return <AlertCircle className="h-3 w-3" />
    }
  }

  // Fetch QR codes from server
  useEffect(() => {
    fetchQRCodes();
    fetchStats();
  }, []);

  // Fetch QR codes based on filters
  useEffect(() => {
    if (showFilters) {
      fetchQRCodes();
    }
  }, [filters]);

  const fetchQRCodes = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();

      // Set a very high limit to fetch all QR codes
      params.append("limit", "10000");
      params.append("page", "1");

      if (searchTerm) params.append("search", searchTerm);
      if (filters.status !== "all") params.append("status", filters.status);
      if (filters.productSize !== "all")
        params.append("productSize", filters.productSize);
      if (filters.pointsRange !== "all")
        params.append("pointsRange", filters.pointsRange);
      if (filters.dateRange !== "all")
        params.append("dateRange", filters.dateRange);

      const apiParams: any = {
        limit: 10000,
        page: 1,
      };

      if (searchTerm) apiParams.search = searchTerm;
      if (filters.status !== "all") apiParams.status = filters.status;
      if (filters.productSize !== "all") apiParams.productSize = filters.productSize;
      if (filters.pointsRange !== "all") apiParams.pointsRange = filters.pointsRange;
      if (filters.dateRange !== "all") apiParams.dateRange = filters.dateRange;

      const response = await qrAPI.getAll(apiParams);

      if (response && response.qrCodes) {
        // Add sNo property for display purposes
        const qrCodesWithSNo = response.qrCodes.map(
          (code: QRCodeData, index: number) => ({
            ...code,
            sNo: index + 1,
            createdDate: code.createdAt
              ? new Date(code.createdAt)
                  .toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "2-digit",
                    year: "2-digit",
                    hour: "2-digit",
                    minute: "2-digit",
                  })
                  .replace(",", "")
              : "",
          })
        );
        setQrCodes(qrCodesWithSNo);
        toast.success('QR codes loaded successfully');
      } else {
        setQrCodes([]);
      }
    } catch (error: any) {
      console.error("Error fetching QR codes:", error);
      const message = error.response?.data?.message || "Error fetching QR codes. Please try again.";
      setError(message);
      toast.error(message);

      if (error.response?.status === 401) {
        localStorage.removeItem('admin_token');
        window.location.href = '/login';
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await qrAPI.getStats();
      if (response) {
        setStats(response);
      }
    } catch (error: any) {
      console.error("Error fetching QR code stats:", error);
      toast.error("Failed to load QR code statistics");
    }
  };

  const generateRandomCode = (prefix: string = "FAS"): string => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = prefix;
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const quantity = parseInt(formData.quantity);

    try {
      setLoading(true);
      console.log("Sending request to generate QR codes:", {
        productSize: formData.productSize,
        quantity,
      });

      const response = await qrAPI.create({
        productSize: formData.productSize,
        quantity,
      });

      console.log("QR code generation response:", response);

      if (response && response.qrCodes) {
        toast.success(`Successfully generated ${quantity} QR codes!`);

        // Download QR images if option is selected
        if (formData.downloadAfterGeneration) {
          await downloadGeneratedQRImages(response.qrCodes);
        }

        await fetchQRCodes();
        await fetchStats();
        setShowForm(false);
        setFormData({
          productSize: "0.75 mm",
          quantity: "1",
          downloadAfterGeneration: false,
        });
      }
    } catch (error: any) {
      console.error("Error generating QR codes:", error);
      const message = error.response?.data?.message || "Error generating QR codes. Please try again.";
      toast.error(message);
      setError(message);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const clearFilters = () => {
    setFilters({
      productSize: "all",
      status: "all",
      dateRange: "all",
      pointsRange: "all",
    });
    // Fetch QR codes after clearing filters
    setTimeout(() => fetchQRCodes(), 100);
  };

  const handleSearch = () => {
    fetchQRCodes();
  };

  const handleQRImport = () => {
    fileInputRef.current?.click();
  };

  const handleFileImport = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (event) => {
      try {
        setLoading(true);
        const data = new Uint8Array(event.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        const importedCodes = jsonData.map((row: any) => ({
          productName:
            row["Product Name"] || `FASTAG CAB WIRE ${row["Size"] || "1.0 mm"}`,
          points: row["Points"] || 10,
          value: row["Value"] || row["Points"] || 10,
          qrCode: row["QR Code"] || generateRandomCode(),
          status: row["Status"] || "Not Redeem",
          productSize: row["Size"] || "1.0 mm",
        }));

        const response = await qrAPI.bulkImport(importedCodes);

        if (response && response.qrCodes) {
          toast.success(
            `Successfully imported ${response.qrCodes.length} QR codes!`
          );
          await fetchQRCodes();
          await fetchStats();
        }
      } catch (error: any) {
        console.error("Error importing file:", error);
        const message = error.response?.data?.message || "Error importing file. Please check the file format.";
        toast.error(message);
        setError(message);
      } finally {
        setLoading(false);
      }
    };
    reader.readAsArrayBuffer(file);
  };

  const exportToExcel = () => {
    const exportData = filteredCodes.map((code) => ({
      "S.No": code.sNo,
      "Product Name": code.productName,
      Points: code.points,
      "Created Date": code.createdDate,
      Value: code.value,
      "QR Code": code.qrCode,
      Status: code.status,
    }));

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "QR Codes");
    XLSX.writeFile(wb, "qr-codes.xlsx");
  };

  const exportQRImage = (qrCode: string, productName: string) => {
    // Create a canvas element
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    if (!ctx) {
      alert("Canvas not supported");
      return;
    }

    // Set canvas size (larger for better quality)
    const size = 512;
    canvas.width = size;
    canvas.height = size;

    // Create QR code using qrcode library
    import("qrcode")
      .then((QRCodeLib) => {
        QRCodeLib.toCanvas(
          canvas,
          qrCode,
          {
            width: size,
            margin: 2,
            color: {
              dark: "#000000",
              light: "#FFFFFF",
            },
          },
          (error) => {
            if (error) {
              console.error("Error generating QR code:", error);
              alert("Error generating QR code image");
              return;
            }

            // Convert canvas to blob and download
            canvas.toBlob((blob) => {
              if (!blob) {
                alert("Error creating image");
                return;
              }

              const url = URL.createObjectURL(blob);
              const link = document.createElement("a");
              link.href = url;
              link.download = `QR_${qrCode}_${productName.replace(
                /[^a-zA-Z0-9]/g,
                "_"
              )}.png`;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              URL.revokeObjectURL(url);
            }, "image/png");
          }
        );
      })
      .catch((error) => {
        console.error("Error loading qrcode library:", error);
        alert("Error loading QR code library");
      });
  };

  const exportAllQRImages = async () => {
    if (filteredCodes.length === 0) {
      alert("No QR codes to export");
      return;
    }

    try {
      setLoading(true);
      setExportProgress({ current: 0, total: filteredCodes.length });
      const zip = new JSZip();
      const QRCodeLib = await import("qrcode");

      // Process QR codes in batches to avoid overwhelming the browser
      const batchSize = 10;
      let processedCount = 0;

      for (let i = 0; i < filteredCodes.length; i += batchSize) {
        const batch = filteredCodes.slice(i, i + batchSize);

        await Promise.all(
          batch.map(async (code) => {
            try {
              // Generate QR code as data URL
              const dataUrl = await QRCodeLib.toDataURL(code.qrCode, {
                width: 512,
                margin: 2,
                color: {
                  dark: "#000000",
                  light: "#FFFFFF",
                },
              });

              // Convert data URL to blob
              const response = await fetch(dataUrl);
              const blob = await response.blob();

              // Add to ZIP with sanitized filename
              const fileName = `QR_${code.qrCode}_${code.productName.replace(
                /[^a-zA-Z0-9]/g,
                "_"
              )}.png`;

              zip.file(fileName, blob);
              processedCount++;
            } catch (error) {
              console.error(`Error processing QR code ${code.qrCode}:`, error);
            }
          })
        );

        // Update progress
        setExportProgress({
          current: processedCount,
          total: filteredCodes.length,
        });
      }

      // Generate ZIP file
      const zipBlob = await zip.generateAsync({ type: "blob" });

      // Download ZIP file
      const url = URL.createObjectURL(zipBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `QR_Codes_Export_${
        new Date().toISOString().split("T")[0]
      }.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      alert(`Successfully exported ${processedCount} QR code images!`);
    } catch (error) {
      console.error("Error exporting QR codes:", error);
      alert("Error exporting QR codes. Please try again.");
    } finally {
      setLoading(false);
      setExportProgress({ current: 0, total: 0 });
    }
  };

  const downloadGeneratedQRImages = async (generatedCodes: QRCodeData[]) => {
    if (generatedCodes.length === 0) return;

    try {
      setLoading(true);
      setExportProgress({ current: 0, total: generatedCodes.length });

      if (generatedCodes.length === 1) {
        // Single QR code - download directly
        const code = generatedCodes[0];
        exportQRImage(code.qrCode, code.productName);
      } else {
        // Multiple QR codes - download as ZIP
        const zip = new JSZip();
        const QRCodeLib = await import("qrcode");

        let processedCount = 0;

        for (const code of generatedCodes) {
          try {
            // Generate QR code as data URL
            const dataUrl = await QRCodeLib.toDataURL(code.qrCode, {
              width: 512,
              margin: 2,
              color: {
                dark: "#000000",
                light: "#FFFFFF",
              },
            });

            // Convert data URL to blob
            const response = await fetch(dataUrl);
            const blob = await response.blob();

            // Add to ZIP with sanitized filename
            const fileName = `QR_${code.qrCode}_${code.productName.replace(
              /[^a-zA-Z0-9]/g,
              "_"
            )}.png`;

            zip.file(fileName, blob);
            processedCount++;

            // Update progress
            setExportProgress({
              current: processedCount,
              total: generatedCodes.length,
            });
          } catch (error) {
            console.error(`Error processing QR code ${code.qrCode}:`, error);
          }
        }

        // Generate ZIP file
        const zipBlob = await zip.generateAsync({ type: "blob" });

        // Download ZIP file
        const url = URL.createObjectURL(zipBlob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `Generated_QR_Codes_${
          new Date().toISOString().split("T")[0]
        }.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Error downloading generated QR codes:", error);
      alert("Error downloading QR codes. Please try again.");
    } finally {
      setLoading(false);
      setExportProgress({ current: 0, total: 0 });
    }
  };

  // Since we're filtering on the server side, we can use qrCodes directly
  const filteredCodes = qrCodes;

  // Loading state
  if (loading && qrCodes.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">QR Code Generator</h1>
            <p className="text-gray-600 mt-1">Generate and manage QR codes for your products</p>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="text-gray-500">Loading QR codes...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error && qrCodes.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">QR Code Generator</h1>
            <p className="text-gray-600 mt-1">Generate and manage QR codes for your products</p>
          </div>
          <button
            onClick={() => {
              setError(null);
              fetchQRCodes();
              fetchStats();
            }}
            className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            <RefreshCw className="h-4 w-4" />
            Retry
          </button>
        </div>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <AlertCircle className="h-12 w-12 text-red-500" />
          <div className="text-center">
            <p className="text-gray-900 font-medium">Failed to load QR codes</p>
            <p className="text-gray-500 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
            <p className="text-lg font-semibold mb-4">
              {exportProgress.total > 0
                ? "Exporting QR Images..."
                : "Loading..."}
            </p>
            {exportProgress.total > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Progress</span>
                  <span>
                    {exportProgress.current} / {exportProgress.total}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${
                        exportProgress.total > 0
                          ? (exportProgress.current / exportProgress.total) *
                            100
                          : 0
                      }%`,
                    }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 text-center">
                  Please wait while we generate your QR code images...
                </p>
              </div>
            )}
          </div>
        </div>
      )}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">QR Code Generator</h1>
          <p className="text-gray-600 mt-1">Generate and manage QR codes for your products</p>
        </div>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => {
              fetchQRCodes();
              fetchStats();
            }}
            disabled={loading}
            className="flex items-center gap-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
          <button
            onClick={handleQRImport}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center space-x-2"
          >
            <Upload className="h-5 w-5" />
            <span>Import QR Codes</span>
          </button>
          <button
            onClick={exportToExcel}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
          >
            <Download className="h-5 w-5" />
            <span>Export Excel</span>
          </button>
          <button
            onClick={exportAllQRImages}
            className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 flex items-center space-x-2"
            disabled={loading || filteredCodes.length === 0}
          >
            <Archive className="h-5 w-5" />
            <span>Download All QR Images</span>
          </button>
          <button
            onClick={() => setShowForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
          >
            <Plus className="h-5 w-5" />
            <span>Generate QR Codes</span>
          </button>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`${
              showFilters ? "bg-blue-700" : "bg-blue-600"
            } text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2`}
          >
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </button>
        </div>

        {/* Hidden file input for QR import */}
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileImport}
          className="hidden"
          accept=".xlsx,.xls,.csv"
        />
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Total QR Codes
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.totalCodes}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Not Redeemed</p>
              <p className="text-2xl font-bold text-green-600">
                {stats.notRedeemed}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Redeemed</p>
              <p className="text-2xl font-bold text-orange-600">
                {stats.redeemed}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Points</p>
              <p className="text-2xl font-bold text-blue-600">
                {stats.totalPoints}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Generate Form */}
      {showForm && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Generate QR Codes
            </h2>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Point System Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">
                Available Point Options
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-xs font-medium text-gray-700 mb-2">
                    Fastagcab Options
                  </h4>
                  <div className="space-y-1 text-xs text-gray-600">
                    <div>0.75mm: 5 pts • 1.0mm: 10 pts • 1.5mm: 15 pts</div>
                    <div>
                      2.5mm: 25 pts • 4.0mm: 40 pts • 6.0mm: 60 pts • 10.0mm:
                      100 pts
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="text-xs font-medium text-gray-700 mb-2">
                    DDH Options
                  </h4>
                  <div className="space-y-1 text-xs text-gray-600">
                    <div>DDH 0.75mm: 5 pts • DDH 1.0mm: 5 pts</div>
                    <div>
                      DDH 1.5mm: 10 pts • DDH 2.5mm: 15 pts • DDH 4.0mm: 20 pts
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label
                  htmlFor="productSize"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Product Size *
                </label>
                <select
                  id="productSize"
                  name="productSize"
                  value={formData.productSize}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <optgroup label="Fastagcab Options">
                    {productOptions
                      .filter((option) => !option.size.startsWith("DDH"))
                      .map((option) => (
                        <option key={option.size} value={option.size}>
                          {option.size} - {option.points} Points
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="DDH Options">
                    {productOptions
                      .filter((option) => option.size.startsWith("DDH"))
                      .map((option) => (
                        <option key={option.size} value={option.size}>
                          {option.size} - {option.points} Points
                        </option>
                      ))}
                  </optgroup>
                </select>
              </div>

              <div>
                <label
                  htmlFor="quantity"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Quantity *
                </label>
                <input
                  type="number"
                  id="quantity"
                  name="quantity"
                  value={formData.quantity}
                  onChange={handleInputChange}
                  required
                  min="1"
                  max="100"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Number of QR codes to generate"
                />
              </div>
            </div>

            {/* Download Option */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="downloadAfterGeneration"
                  name="downloadAfterGeneration"
                  checked={formData.downloadAfterGeneration}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label
                  htmlFor="downloadAfterGeneration"
                  className="text-sm font-medium text-gray-700"
                >
                  Automatically download QR code images after generation
                </label>
              </div>
              <p className="text-xs text-gray-600 mt-2 ml-6">
                When enabled, QR code images will be automatically downloaded as
                PNG files. Single QR codes download directly, multiple QR codes
                download as a ZIP file.
              </p>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => setShowForm(false)}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Generate QR Codes
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex gap-4 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search QR codes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <button
            onClick={handleSearch}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
          >
            <Search className="h-4 w-4" />
            <span>Search</span>
          </button>
          <button
            onClick={() => {
              setSearchTerm("");
              clearFilters();
              fetchQRCodes();
            }}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
          >
            <span>Show All QR Codes</span>
          </button>
        </div>

        {/* Filter Section */}
        {showFilters && (
          <div className="mt-4 border-t pt-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-medium text-gray-900">
                Filter QR Codes
              </h3>
              <button
                onClick={clearFilters}
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
              >
                <X className="h-4 w-4 mr-1" />
                Clear Filters
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Product Size Filter */}
              <div>
                <label
                  htmlFor="productSize"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Product Size
                </label>
                <select
                  id="productSize"
                  name="productSize"
                  value={filters.productSize}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Sizes</option>
                  <optgroup label="Fastagcab Options">
                    {productOptions
                      .filter((option) => !option.size.startsWith("DDH"))
                      .map((option) => (
                        <option key={option.size} value={option.size}>
                          {option.size} - {option.points} Points
                        </option>
                      ))}
                  </optgroup>
                  <optgroup label="DDH Options">
                    {productOptions
                      .filter((option) => option.size.startsWith("DDH"))
                      .map((option) => (
                        <option key={option.size} value={option.size}>
                          {option.size} - {option.points} Points
                        </option>
                      ))}
                  </optgroup>
                </select>
              </div>

              {/* Status Filter */}
              <div>
                <label
                  htmlFor="status"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={filters.status}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="Not Redeem">Not Redeemed</option>
                  <option value="Redeemed">Redeemed</option>
                </select>
              </div>

              {/* Points Range Filter */}
              <div>
                <label
                  htmlFor="pointsRange"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Points Range
                </label>
                <select
                  id="pointsRange"
                  name="pointsRange"
                  value={filters.pointsRange}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Points</option>
                  <option value="low">Low (≤ 15)</option>
                  <option value="medium">Medium (16-40)</option>
                  <option value="high">High (&gt; 40)</option>
                </select>
              </div>

              {/* Date Range Filter */}
              <div>
                <label
                  htmlFor="dateRange"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Date Range
                </label>
                <select
                  id="dateRange"
                  name="dateRange"
                  value={filters.dateRange}
                  onChange={handleFilterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Dates</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* QR Codes Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">QR Code List</h3>
          <p className="text-sm text-gray-500">
            Showing {filteredCodes.length} of {stats.totalCodes} QR codes
          </p>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-blue-600">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  S.No
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Product Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Points
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Created Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  QR Code
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  QR Image
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCodes.map((code) => (
                <tr key={code._id || code.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {code.sNo}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {code.productName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {code.points}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {code.createdDate}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {code.value}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {code.qrCode}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      {code.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="w-16 h-16">
                      <QRCode
                        value={code.qrCode}
                        size={64}
                        style={{
                          height: "auto",
                          maxWidth: "100%",
                          width: "100%",
                        }}
                      />
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() =>
                        exportQRImage(code.qrCode, code.productName)
                      }
                      className="bg-blue-600 text-white px-3 py-1 rounded-lg hover:bg-blue-700 flex items-center space-x-1 text-xs"
                      title="Download QR Code Image"
                    >
                      <Download className="h-3 w-3" />
                      <span>Download</span>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default QRCodes;