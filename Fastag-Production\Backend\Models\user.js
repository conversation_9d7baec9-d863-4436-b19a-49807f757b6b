import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

const userSchema = new mongoose.Schema({
    fullName: {
        type: String,
        required: [true, 'Full name is required'],
        trim: true,
        minlength: [2, 'Full name must be at least 2 characters long'],
        maxlength: [100, 'Full name cannot exceed 100 characters']
    },
    phoneNumber: {
        type: String,
        required: [true, 'Phone number is required'],
        unique: true,
        trim: true,
        match: [
            /^[6-9]\d{9}$/,
            'Please enter a valid 10-digit Indian phone number'
        ]
    },
    password: {
        type: String,
        required: [true, 'Password is required'],
        minlength: [6, 'Password must be at least 6 characters long']
    },
    dateOfBirth: {
        type: Date,
        required: [true, 'Date of birth is required']
    },
    age: {
        type: Number,
        required: [true, 'Age is required'],
        min: [18, 'Age must be at least 18'],
        max: [100, 'Age cannot exceed 100']
    },
    adharNumber: {
        type: String,
        trim: true,
        unique: true,
        sparse: true, // Allow multiple null values but unique non-null values
        match: [
            /^\d{12}$/,
            'Adhar number must be 12 digits'
        ]
    },
    panCardNumber: {
        type: String,
        trim: true,
        uppercase: true,
        unique: true,
        sparse: true, // Allow multiple null values but unique non-null values
        match: [
            /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
            'Please enter a valid PAN card number'
        ]
    },
    pinCode: {
        type: String,
        required: [true, 'Pin code is required'],
        trim: true,
        match: [
            /^\d{6}$/,
            'Pin code must be 6 digits'
        ]
    },
    state: {
        type: String,
        required: [true, 'State is required'],
        trim: true
    },
    city: {
        type: String,
        required: [true, 'City is required'],
        trim: true
    },
    address: {
        type: String,
        required: [true, 'Address is required'],
        trim: true,
        maxlength: [500, 'Address cannot exceed 500 characters']
    },
    dealerCode: {
        type: String,
        required: [true, 'Dealer code is required'],
        trim: true,
        uppercase: true,
        unique: true
    },
    role: {
        type: String,
        enum: ['Electrician', 'Distributor', 'admin'],
        default: 'Electrician'
    },
    // File upload fields
    profilePhoto: {
        type: String,
        required: [true, 'Profile photo is required']
    },
    adharCard: {
        type: String
    },
    panCard: {
        type: String
    },
    bankDetails: {
        type: String
    },
    // Status and verification
    status: {
        type: String,
        enum: ['pending', 'approved', 'rejected'],
        default: 'pending'
    },
    isVerified: {
        type: Boolean,
        default: false
    },
    monthlyPoints: {
        type: Number,
        default: 0
    },
    yearlyPoints: {
        type: Number,
        default: 0
    },
    registrationAnniversary: {
        type: Date,
        default: Date.now,
        index: true
    },
    lastMonthlyReset: {
        type: Date,
        default: null
    },
    yearlyPointsResetAt: {
        type: Date,
        default: null
    },
    scannedQRCodes: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'QRCode'
    }]
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Virtual for checking if monthly reset is needed
userSchema.virtual('needsMonthlyReset').get(function() {
    if (!this.lastMonthlyReset) return true;

    const now = new Date();
    const lastReset = new Date(this.lastMonthlyReset);

    // Check if we're in a new month
    return now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear();
});

// Virtual for checking if yearly reset is needed
userSchema.virtual('needsYearlyReset').get(function() {
    if (!this.yearlyPointsResetAt) return false;

    const now = new Date();
    const yearlyReset = new Date(this.yearlyPointsResetAt);

    return now >= yearlyReset;
});

// Virtual for next monthly reset date
userSchema.virtual('nextMonthlyReset').get(function() {
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    return nextMonth;
});

// Virtual for next yearly reset date
userSchema.virtual('nextYearlyReset').get(function() {
    if (!this.registrationAnniversary) return null;

    const now = new Date();
    const anniversary = new Date(this.registrationAnniversary);
    const nextYear = new Date(now.getFullYear() + 1, anniversary.getMonth(), anniversary.getDate());

    return nextYear;
});

// Instance method to reset monthly points
userSchema.methods.resetMonthlyPoints = function() {
    this.monthlyPoints = 0;
    this.lastMonthlyReset = new Date();
    return this.save();
};

// Instance method to reset yearly points (DISABLED - yearly points don't auto-reset)
// Yearly points only decrease when user redeems them, they don't auto-reset after 1 year
userSchema.methods.resetYearlyPoints = function() {
    // DO NOT reset yearly points automatically
    // Yearly points should only be reduced through redemption
    console.log('WARNING: resetYearlyPoints called but yearly points should not auto-reset');
    return Promise.resolve(this);
};

// Instance method to add points (both monthly and yearly)
userSchema.methods.addPoints = function(points) {
    this.monthlyPoints += points;
    this.yearlyPoints += points;
    return this;
};

// Instance method to deduct points with different logic for recharge vs gifts
userSchema.methods.deductPoints = function(points, redemptionType = 'gift') {
    let deductedFromMonthly = 0;
    let deductedFromYearly = 0;

    if (redemptionType === 'recharge') {
        // For recharge: deduct the same amount from both monthly and yearly points
        deductedFromMonthly = Math.min(this.monthlyPoints, points);
        deductedFromYearly = Math.min(this.yearlyPoints, points);

        this.monthlyPoints -= deductedFromMonthly;
        this.yearlyPoints -= deductedFromYearly;

        this._lastDeduction = {
            totalRequested: points,
            deductedFromMonthly,
            deductedFromYearly,
            totalDeducted: points, // For recharge, we consider the full amount deducted if both balances were sufficient
            remainingUndeducted: Math.max(0, points - Math.min(deductedFromMonthly, deductedFromYearly)),
            redemptionType: 'recharge'
        };
    } else {
        // For gifts: deduct the same amount from both monthly and yearly points
        // User must have sufficient yearly points to redeem gifts
        deductedFromMonthly = Math.min(this.monthlyPoints, points);
        deductedFromYearly = Math.min(this.yearlyPoints, points);

        this.monthlyPoints -= deductedFromMonthly;
        this.yearlyPoints -= deductedFromYearly;

        this._lastDeduction = {
            totalRequested: points,
            deductedFromMonthly,
            deductedFromYearly,
            totalDeducted: points, // For gifts, we consider the full amount deducted if yearly points were sufficient
            remainingUndeducted: Math.max(0, points - deductedFromYearly), // Based on yearly points availability
            redemptionType: 'gift'
        };
    }

    return this;
};

// Instance method to check if user has sufficient points
userSchema.methods.hasSufficientPoints = function(requiredPoints, pointType = 'combined', redemptionType = 'gift') {
    switch (pointType) {
        case 'monthly':
            return this.monthlyPoints >= requiredPoints;
        case 'yearly':
            return this.yearlyPoints >= requiredPoints;
        case 'recharge':
            // For recharge: need minimum 500 monthly points AND sufficient points in both monthly and yearly balances
            return this.monthlyPoints >= 500 && this.monthlyPoints >= requiredPoints && this.yearlyPoints >= requiredPoints;
        case 'gift':
            // For gifts: need sufficient yearly points (monthly points will be deducted up to available amount)
            return this.yearlyPoints >= requiredPoints;
        case 'both':
            // Old logic: both balances must have enough points individually
            return this.monthlyPoints >= requiredPoints && this.yearlyPoints >= requiredPoints;
        case 'combined':
        default:
            // New logic: total available points (monthly + yearly) must be enough
            return (this.monthlyPoints + this.yearlyPoints) >= requiredPoints;
    }
};

// Hash password before saving
userSchema.pre('save', async function(next) {
    // Only hash the password if it has been modified (or is new)
    if (!this.isModified('password')) return next();

    try {
        // Hash password with cost of 12
        const hashedPassword = await bcrypt.hash(this.password, 12);
        this.password = hashedPassword;
        next();
    } catch (error) {
        next(error);
    }
});

// Pre-save middleware to set registration anniversary for new users
userSchema.pre('save', function(next) {
    if (this.isNew && !this.registrationAnniversary) {
        this.registrationAnniversary = new Date();
        // Set yearly reset to one year from registration
        const yearFromNow = new Date();
        yearFromNow.setFullYear(yearFromNow.getFullYear() + 1);
        this.yearlyPointsResetAt = yearFromNow;
    }
    next();
});

// Instance method to check password
userSchema.methods.comparePassword = async function(candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
};

// Remove password from JSON output
userSchema.methods.toJSON = function() {
    const userObject = this.toObject();
    delete userObject.password;
    return userObject;
};

const User = mongoose.model("User", userSchema);
export default User;
